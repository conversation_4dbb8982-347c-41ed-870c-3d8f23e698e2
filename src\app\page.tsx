import Image from "next/image";
import { promises as fs } from 'fs';
import os from 'os';

interface ConfigInfo {
  environment: Record<string, string>;
  nodeInfo: {
    version: string;
    platform: string;
    arch: string;
    uptime: string;
    memory: {
      total: string;
      free: string;
      used: string;
    };
  };
  dockerInfo: {
    isInContainer: boolean;
    containerInfo?: any;
  };
  appConfig: {
    name: string;
    version: string;
    nodeEnv: string;
    port: string;
    hostname: string;
  };
}

async function getConfigInfo(): Promise<ConfigInfo> {
  // Get environment variables (filter sensitive ones)
  const sensitiveKeys = ['PASSWORD', 'SECRET', 'KEY', 'TOKEN', 'PRIVATE'];
  const environment = Object.entries(process.env)
    .filter(([key]) => !sensitiveKeys.some(sensitive => key.toUpperCase().includes(sensitive)))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value || '' }), {});

  // Get Node.js and system info
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;

  const nodeInfo = {
    version: process.version,
    platform: os.platform(),
    arch: os.arch(),
    uptime: `${Math.floor(process.uptime())} seconds`,
    memory: {
      total: `${Math.round(totalMem / 1024 / 1024)} MB`,
      free: `${Math.round(freeMem / 1024 / 1024)} MB`,
      used: `${Math.round(usedMem / 1024 / 1024)} MB`,
    },
  };

  // Check if running in Docker container
  let dockerInfo: ConfigInfo['dockerInfo'] = { isInContainer: false };
  try {
    // Check for Docker-specific files/indicators
    const cgroupContent = await fs.readFile('/proc/1/cgroup', 'utf8').catch(() => '');
    const isInContainer = cgroupContent.includes('docker') ||
                         cgroupContent.includes('containerd') ||
                         process.env.DOCKER_CONTAINER === 'true' ||
                         await fs.access('/.dockerenv').then(() => true).catch(() => false);

    dockerInfo.isInContainer = isInContainer;

    if (isInContainer) {
      dockerInfo.containerInfo = {
        hostname: os.hostname(),
        workingDirectory: process.cwd(),
        dockerEnv: process.env.DOCKER_CONTAINER || 'detected',
      };
    }
  } catch (error) {
    // If we can't read container info, assume not in container
    dockerInfo.isInContainer = false;
  }

  // Get application configuration
  const appConfig = {
    name: process.env.npm_package_name || 'next-docker-test',
    version: process.env.npm_package_version || '0.1.0',
    nodeEnv: process.env.NODE_ENV || 'development',
    port: process.env.PORT || '3000',
    hostname: process.env.HOSTNAME || os.hostname(),
  };

  return {
    environment,
    nodeInfo,
    dockerInfo,
    appConfig,
  };
}

function ConfigSection({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 border-b pb-2">
        {title}
      </h2>
      {children}
    </div>
  );
}

function ConfigItem({ label, value }: { label: string; value: string }) {
  return (
    <div className="flex flex-col sm:flex-row sm:justify-between py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
      <span className="font-medium text-gray-700 dark:text-gray-300 mb-1 sm:mb-0">
        {label}:
      </span>
      <span className="text-gray-600 dark:text-gray-400 font-mono text-sm break-all">
        {value}
      </span>
    </div>
  );
}

export default async function Home() {
  const config = await getConfigInfo();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <Image
            className="dark:invert mx-auto mb-4"
            src="/next.svg"
            alt="Next.js logo"
            width={180}
            height={38}
            priority
          />
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
            Runtime Configuration Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Application runtime environment and configuration details
          </p>
        </div>

        {/* Application Configuration */}
        <ConfigSection title="Application Configuration">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ConfigItem label="Application Name" value={config.appConfig.name} />
            <ConfigItem label="Version" value={config.appConfig.version} />
            <ConfigItem label="Environment" value={config.appConfig.nodeEnv} />
            <ConfigItem label="Port" value={config.appConfig.port} />
            <ConfigItem label="Hostname" value={config.appConfig.hostname} />
          </div>
        </ConfigSection>

        {/* Docker Information */}
        <ConfigSection title="Docker Container Information">
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">
                Container Status:
              </span>
              <span className={`px-2 py-1 rounded text-sm font-medium ${
                config.dockerInfo.isInContainer
                  ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
              }`}>
                {config.dockerInfo.isInContainer ? 'Running in Container' : 'Not in Container'}
              </span>
            </div>
          </div>

          {config.dockerInfo.isInContainer && config.dockerInfo.containerInfo && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ConfigItem
                label="Container Hostname"
                value={config.dockerInfo.containerInfo.hostname}
              />
              <ConfigItem
                label="Working Directory"
                value={config.dockerInfo.containerInfo.workingDirectory}
              />
              <ConfigItem
                label="Docker Environment"
                value={config.dockerInfo.containerInfo.dockerEnv}
              />
            </div>
          )}
        </ConfigSection>

        {/* Node.js Runtime Information */}
        <ConfigSection title="Node.js Runtime Information">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ConfigItem label="Node.js Version" value={config.nodeInfo.version} />
            <ConfigItem label="Platform" value={config.nodeInfo.platform} />
            <ConfigItem label="Architecture" value={config.nodeInfo.arch} />
            <ConfigItem label="Process Uptime" value={config.nodeInfo.uptime} />
            <ConfigItem label="Total Memory" value={config.nodeInfo.memory.total} />
            <ConfigItem label="Free Memory" value={config.nodeInfo.memory.free} />
            <ConfigItem label="Used Memory" value={config.nodeInfo.memory.used} />
          </div>
        </ConfigSection>

        {/* Environment Variables */}
        <ConfigSection title="Environment Variables">
          <div className="max-h-96 overflow-y-auto">
            <div className="grid grid-cols-1 gap-2">
              {Object.entries(config.environment)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(([key, value]) => (
                  <ConfigItem key={key} label={key} value={value} />
                ))}
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
            * Sensitive environment variables (containing PASSWORD, SECRET, KEY, TOKEN, PRIVATE) are filtered out for security.
          </div>
        </ConfigSection>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 dark:text-gray-400">
          <p className="text-sm">
            Last updated: {new Date().toLocaleString()}
          </p>
          <p className="text-xs mt-2">
            This page displays runtime configuration for debugging and monitoring purposes.
          </p>
        </div>
      </div>
    </div>
  );
}
